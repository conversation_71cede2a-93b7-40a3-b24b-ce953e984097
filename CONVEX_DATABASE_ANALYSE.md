# JobbLogg Convex Database Analyse

## Oversikt
JobbLogg bruker Convex som backend-database med en strukturert datamodell som støtter prosjektadministrasjon, kundebehandling, chat-funksjonalitet og deling av prosjekter. Databasen er designet for å håndtere både private og bedriftskunder med omfattende metadata og relasjoner.

## Database Schema

### TypeScript Interfaces for Type Safety

JobbLogg bruker eksplisitte TypeScript interfaces for bedre type-sikkerhet og dokumentasjon:

```typescript
// Deling og tilgangsstatistikk
export interface ShareSettings {
  showContractorNotes: boolean;
  accessCount: number;
  lastAccessedAt?: number; // millisekunder siden Unix epoch
}

// Jobbdata med bilder og notater
export interface JobData {
  jobDescription: string;
  photos: JobPhoto[];
  accessNotes: string;
  equipmentNeeds: string;
  unresolvedQuestions: string;
  personalNotes: string;
}

// Chat-filvedlegg
export interface MessageFile {
  url: string;
  name: string;
  size: number;
  type: string;
  thumbnailUrl?: string;
}

// Chat-reaksjoner
export interface MessageReaction {
  emoji: string;
  userIds: string[];
  count: number;
}

// Enum-typer for bedre type-sikkerhet
export type CustomerType = "privat" | "bedrift";
export type DeliveryStatus = "sending" | "sent" | "delivered" | "failed";
export type SenderRole = "customer" | "contractor";
```

### 1. Customers (Kunder)
**Formål:** Lagrer kunde- og bedriftsinformasjon med støtte for Brønnøysundregisteret-integrasjon

**Felter:**
- `name: string` - Kundenavn (påkrevd)
- `type: "privat" | "bedrift"` - Kundetype (forenklet fra tidligere "firma" | "bedrift")
- `contactPerson?: string` - Kontaktperson for bedrifter
- `phone?: string` - Telefonnummer
- `email?: string` - E-postadresse
- `streetAddress?: string` - Gateadresse
- `postalCode?: string` - Postnummer
- `city?: string` - By/sted
- `entrance?: string` - Inngang/etasje info
- `orgNumber?: string` - Organisasjonsnummer
- `notes?: string` - Notater
- `brregFetchedAt?: number` - Tidsstempel for Brreg-data (millisekunder siden Unix epoch)
- `brregData?: BrregData` - Originale Brønnøysundregisteret-data med strukturert interface
- `useCustomAddress?: boolean` - Om bruker overstyrer Brreg-adresse
- `userId: string` - Eier av kundeposten
- `createdAt: number` - Opprettelsestidspunkt (millisekunder siden Unix epoch)

**Indekser:**
- `by_user` - Effektiv henting av brukerens kunder
- `by_type` - Filtrering på kundetype
- `by_user_and_type` - Kombinert bruker/type-filtrering
- `by_org_number` - Søk på organisasjonsnummer

### 2. Projects (Prosjekter)
**Formål:** Hovedentitet for prosjektadministrasjon med arkivering og deling

**Felter:**
- `name: string` - Prosjektnavn
- `description: string` - Prosjektbeskrivelse
- `userId: string` - Prosjekteier
- `customerId?: Id<"customers">` - **Relasjon til kunde**
- `sharedId: string` - Unik ID for deling
- `createdAt: number` - Opprettelsestidspunkt (millisekunder siden Unix epoch)
- `isArchived?: boolean` - Arkivstatus
- `archivedAt?: number` - Arkiveringstidspunkt (millisekunder siden Unix epoch)
- `archivedBy?: string` - Hvem som arkiverte
- `isPubliclyShared?: boolean` - Om prosjektet er offentlig delt
- `shareSettings?: ShareSettings` - Delingsinnstillinger og statistikk (definert interface)
- `jobData?: JobData` - Jobbinformasjon med strukturert interface

**Indekser:**
- `by_user` - Brukerens prosjekter
- `by_shared_id` - Tilgang via delt lenke
- `by_customer` - Prosjekter for spesifikk kunde
- `by_user_and_customer` - Kombinert bruker/kunde-filtrering
- `by_user_and_archive_status` - Aktive vs arkiverte prosjekter
- `by_archived_status` - Global arkivstatus

### 3. LogEntries (Loggoppføringer)
**Formål:** Prosjektlogg med bilder og redigeringshistorikk

**Felter:**
- `projectId: Id<"projects">` - **Relasjon til prosjekt**
- `userId: string` - Opprettet av bruker
- `description: string` - Loggbeskrivelse
- `imageId?: Id<"_storage">` - **Relasjon til lagret bilde**
- `createdAt: number` - Opprettelsestidspunkt (millisekunder siden Unix epoch)
- `entryType?: "user" | "system"` - Type oppføring (enum)
- `isEdited?: boolean` - Om oppføringen er redigert
- `lastEditedAt?: number` - Siste redigeringstidspunkt (millisekunder siden Unix epoch)
- `editHistory?: EditHistoryEntry[]` - Komplett redigeringshistorikk med strukturert interface

**Indekser:**
- `by_project` - Alle logger for et prosjekt
- `by_user` - Brukerens logger
- `by_project_and_user` - Kombinert prosjekt/bruker-filtrering

### 4. Messages (Chat-meldinger)
**Formål:** Trådet chat-system knyttet til loggoppføringer

**Felter:**
- `logId: Id<"logEntries">` - **Relasjon til loggoppføring**
- `parentId?: Id<"messages">` - **Selvrelasjon for tråding**
- `senderId: string` - Avsender-ID
- `senderRole: "customer" | "contractor"` - Avsenderrolle (enum)
- `text?: string` - Meldingstekst (påkrevd hvis ingen fil)
- `file?: MessageFile` - Filvedlegg med strukturert interface (påkrevd hvis ingen tekst)
- `reactions?: MessageReaction[]` - Emoji-reaksjoner med strukturert interface
- `readBy?: Record<string, number>` - Lesestatus per bruker (userId → timestamp)
- `deliveryStatus?: "sending" | "sent" | "delivered" | "failed"` - Leveringsstatus (enum)
- `createdAt: number` - Opprettelsestidspunkt (millisekunder siden Unix epoch)
- `isEdited?: boolean` - Redigeringsstatus
- `isDeleted?: boolean` - Slettestatus (soft delete)

**Indekser:**
- `by_log` - Alle meldinger for en logg
- `by_parent` - Svar på en melding
- `by_sender` - Meldinger fra en avsender
- `by_log_and_created` - Kronologisk sortering per logg
- `by_log_and_parent` - Trådstruktur-spørringer

### 5. ImageLikes (Bilde-likes)
**Formål:** Kundereaksjoner på bilder i delte prosjekter

**Felter:**
- `logEntryId: Id<"logEntries">` - **Relasjon til loggoppføring**
- `projectId: Id<"projects">` - **Relasjon til prosjekt**
- `sharedId: string` - Validering av delt tilgang
- `customerSessionId: string` - Anonym kunde-ID
- `customerName?: string` - Valgfritt kundenavn
- `customerEmail?: string` - Valgfri kunde-e-post
- `createdAt: number` - Like-tidspunkt (millisekunder siden Unix epoch)
- `ipAddress?: string` - IP for spam-forebygging

**Indekser:**
- `by_log_entry` - Likes for en loggoppføring
- `by_project` - Likes for et prosjekt
- `by_shared_id` - Likes via delt lenke
- `by_customer_session` - Likes fra en kunde-sesjon

### 6. Støttetabeller

**TypingIndicators:** Real-time typing-indikatorer for chat
- `logId: Id<"logEntries">` - Loggoppføring hvor bruker skriver
- `userId: string` - Bruker som skriver
- `userRole: "customer" | "contractor"` - Brukerrolle
- `expiresAt: number` - Utløpstidspunkt (millisekunder siden Unix epoch)
- `createdAt: number` - Starttidspunkt (millisekunder siden Unix epoch)
- `updatedAt: number` - Siste aktivitetstidspunkt (millisekunder siden Unix epoch)

**LinkPreviews:** OpenGraph metadata-cache for URL-forhåndsvisninger
- `url: string` - Original URL
- `title?: string` - OpenGraph tittel
- `description?: string` - OpenGraph beskrivelse
- `image?: string` - OpenGraph bilde-URL
- `domain: string` - Ekstrahert domene for visning
- `cachedAt: number` - Cachetidspunkt (millisekunder siden Unix epoch)
- `expiresAt: number` - Utløpstidspunkt (millisekunder siden Unix epoch)

## Relasjonsanalyse

### Primære Relasjoner (v.id() referanser)

1. **Projects → Customers** (én-til-mange)
   - `projects.customerId → customers._id`
   - Et prosjekt kan ha én kunde, en kunde kan ha mange prosjekter

2. **LogEntries → Projects** (mange-til-én)
   - `logEntries.projectId → projects._id`
   - Mange loggoppføringer per prosjekt

3. **Messages → LogEntries** (mange-til-én)
   - `messages.logId → logEntries._id`
   - Chat-tråder knyttet til spesifikke loggoppføringer

4. **Messages → Messages** (selvrelasjon, mange-til-én)
   - `messages.parentId → messages._id`
   - Hierarkisk trådstruktur for svar

5. **ImageLikes → LogEntries** (mange-til-én)
   - `imageLikes.logEntryId → logEntries._id`
   - Kunde-likes på bilder i logger

6. **ImageLikes → Projects** (mange-til-én)
   - `imageLikes.projectId → projects._id`
   - Direkte kobling for effektiv statistikk

### Indirekte Relasjoner

1. **Users → All Tables** (via userId string)
   - Alle tabeller har `userId` for tilgangskontroll
   - Ikke formell relasjon, men kritisk for autorisering

2. **SharedId → Projects** (via sharedId string)
   - Offentlig tilgang til prosjekter uten autentisering
   - Brukes i `imageLikes` og `logEntries` for validering

## API-struktur

### Queries (Datahenting)
- **customers.ts:** `getByUser`, `getById`, `search`, `getStats`
- **projects.ts:** `getByUser`, `getBySharedId`, `getByCustomer`, `getArchivedByUser`
- **logEntries.ts:** `getByProject`, `getBySharedProject`, `getLogEntryHistory`
- **messages.ts:** `getMessagesByLog`, `getMessagesWithDisplayNames`, `getUnreadCounts`
- **imageLikes.ts:** `getLikesForProject`, `getLikesForSharedProject`

### Mutations (Dataendringer)
- **customers.ts:** `create`, `update`, `delete`
- **projects.ts:** `create`, `update`, `archiveProject`, `restoreProject`, `toggleSharing`
- **logEntries.ts:** `create`, `update`, `delete`, `generateUploadUrl`
- **messages.ts:** `sendMessage`, `editMessage`, `deleteMessage`, `addReaction`, `markAsRead`
- **imageLikes.ts:** `toggleLike`

## React Integration Eksempler

### useQuery Hook Bruk
```typescript
// Hent prosjekter med kunde-data
const projects = useQuery(api.projects.getByUserWithCustomers, { 
  userId: user?.id || "" 
});

// Hent chat-meldinger for en logg
const messagesData = useQuery(api.messages.getMessagesWithDisplayNames, {
  logId,
  userId,
  userRole: "contractor",
  limit: 50
});
```

### useMutation Hook Bruk
```typescript
// Opprett nytt prosjekt
const createProject = useMutation(api.projects.create);

// Send chat-melding
const sendMessage = useMutation(api.messages.sendMessage);

// Toggle kunde-like på bilde
const toggleLike = useMutation(api.imageLikes.toggleLike);
```

## Kjernefunksjonalitet

### Prosjektadministrasjon
- Opprettelse med kunde-kobling
- Arkivering/gjenåpning med historikk
- Offentlig deling med tilgangskontroll
- Statistikk og aktivitetssporing

### Chat-system
- Trådet samtaler per loggoppføring
- Real-time meldinger med reaksjoner
- Filvedlegg og URL-forhåndsvisninger
- Lesestatus og leveringsbekreftelser

### Kunde-engasjement
- Anonyme likes på delte prosjekter
- Sesjon-basert sporing
- Statistikk for entreprenører

### Dataintegritet
- Soft delete for meldinger
- Redigeringshistorikk for logger
- Arkivering fremfor sletting
- Omfattende indeksering for ytelse

Denne datamodellen støtter JobbLoggs komplette arbeidsflyt fra kunderegistrering til prosjektgjennomføring og kunde-kommunikasjon, med robust støtte for deling og engasjement.

## Relasjonsdiagram

```mermaid
erDiagram
    CUSTOMERS {
        string _id PK
        string name
        CustomerType type "privat | bedrift"
        string contactPerson
        string phone
        string email
        string streetAddress
        string postalCode
        string city
        string orgNumber
        string userId
        number createdAt "milliseconds since Unix epoch"
    }

    PROJECTS {
        string _id PK
        string name
        string description
        string userId
        string customerId FK
        string sharedId
        number createdAt "milliseconds since Unix epoch"
        boolean isArchived
        boolean isPubliclyShared
        ShareSettings shareSettings "structured interface"
        JobData jobData "structured interface"
    }

    LOG_ENTRIES {
        string _id PK
        string projectId FK
        string userId
        string description
        string imageId FK
        number createdAt
        boolean isEdited
    }

    MESSAGES {
        string _id PK
        string logId FK
        string parentId FK
        string senderId
        SenderRole senderRole "customer | contractor"
        string text "required if no file"
        MessageFile file "required if no text"
        MessageReaction[] reactions "structured array"
        DeliveryStatus deliveryStatus "enum"
        number createdAt "milliseconds since Unix epoch"
        boolean isDeleted
    }

    IMAGE_LIKES {
        string _id PK
        string logEntryId FK
        string projectId FK
        string sharedId
        string customerSessionId
        number createdAt
    }

    CUSTOMERS ||--o{ PROJECTS : "har"
    PROJECTS ||--o{ LOG_ENTRIES : "inneholder"
    LOG_ENTRIES ||--o{ MESSAGES : "har chat"
    MESSAGES ||--o{ MESSAGES : "svar på"
    LOG_ENTRIES ||--o{ IMAGE_LIKES : "kan likes"
    PROJECTS ||--o{ IMAGE_LIKES : "samler likes"
```

## Dataflyt og Bruksmønstre

### 1. Prosjektopprettelse med Kunde
```typescript
// Steg 1: Opprett eller velg kunde
const customerId = await createCustomer({
  name: "Ola Nordmann",
  type: "privat",
  phone: "+47 123 45 678",
  streetAddress: "Storgata 1",
  postalCode: "0123",
  city: "Oslo",
  userId: user.id
});

// Steg 2: Opprett prosjekt med kunde-kobling
const projectId = await createProject({
  name: "Kjøkkenrenovering",
  description: "Komplett renovering av kjøkken",
  userId: user.id,
  customerId: customerId // Relasjon etableres
});
```

### 2. Loggføring med Chat
```typescript
// Opprett loggoppføring
const logEntryId = await createLogEntry({
  projectId: projectId,
  userId: user.id,
  description: "Startet arbeid med kjøkkenskap",
  imageId: uploadedImageId
});

// Start chat-tråd på loggoppføringen
const messageId = await sendMessage({
  logId: logEntryId, // Kobling til logg
  senderId: user.id,
  senderRole: "contractor",
  text: "Arbeidet går som planlagt!"
});
```

### 3. Kunde-engasjement via Deling
```typescript
// Del prosjekt offentlig
await toggleSharing({
  projectId: projectId,
  isPubliclyShared: true,
  shareSettings: {
    showContractorNotes: true,
    accessCount: 0
  }
});

// Kunde liker bilde (anonym)
await toggleLike({
  logEntryId: logEntryId,
  projectId: projectId,
  sharedId: project.sharedId,
  customerSessionId: "anon_customer_123"
});
```

## Ytelsesoptimalisering

### Indeks-strategi
1. **Sammensatte indekser** for vanlige spørringer:
   - `by_user_and_archive_status` - Dashboard-visning
   - `by_log_and_created` - Chat-meldinger kronologisk
   - `by_user_and_customer` - Kunde-spesifikke prosjekter

2. **Enkle indekser** for direkte oppslag:
   - `by_shared_id` - Offentlig tilgang
   - `by_org_number` - Bedriftssøk

### Query-optimalisering
- **Batch-henting** av relaterte data (prosjekter med kunder)
- **Paginering** for store datasett (chat-meldinger)
- **Conditional queries** med "skip" for unødvendige kall

## Sikkerhet og Tilgangskontroll

### Autorisering per Tabell
1. **Customers/Projects/LogEntries:** `userId`-basert eierskap
2. **Messages:** Rolle-basert tilgang (contractor/customer)
3. **ImageLikes:** Validering via `sharedId`

### Dataintegritet
- **Soft delete** for meldinger (bevarer chat-historikk)
- **Arkivering** fremfor sletting (juridisk dokumentasjon)
- **Redigeringshistorikk** for sporbarhet

## Fremtidige Utvidelser

### Potensielle Forbedringer
1. **Notifikasjoner:** Ny tabell for push-varsler
2. **Fakturering:** Kobling til økonomi-systemer
3. **Tidsregistrering:** Arbeidstimer per prosjekt
4. **Dokumenter:** Kontrakter og avtaler
5. **Kalender:** Avtaler og milepæler

### Skaleringsoverveielser
- **Partisjonering** av store tabeller (messages, logEntries)
- **Caching** av ofte-brukte data (kunde-info, prosjekt-metadata)
- **Arkivering** av gamle data til kald lagring

## Nylige Forbedringer

### 1. Forenklet Kundetype-enum
- Fjernet "firma" fra `type: "privat" | "firma" | "bedrift"` enum i customers-tabellen
- Beholdt kun "privat" og "bedrift" siden begge "firma" og "bedrift" betyr det samme på norsk
- Oppdatert alle relaterte queries, mutations og React-komponenter
- Lagt til migrasjonsskript for å konvertere eksisterende "firma"-poster til "bedrift"

### 2. Spesifiserte Object-feltenes TypeScript-interfaces
- Definert eksplisitte interfaces for tidligere vagt definerte object-felter:
  - `shareSettings?: object` → `ShareSettings` interface
  - `jobData?: object` → `JobData` interface
  - `file?: object` → `MessageFile` interface
  - `reactions?: array` → `MessageReaction[]` array
  - `readBy?: record` → `Record<string, number>` med userId som nøkkel

### 3. Standardiserte Navnekonvensjoner
- Harmonisert mellom camelCase i kode og snake_case i ER-diagram
- Valgt én konsistent konvensjon for hele prosjektet
- Rettet opp inkonsistente API-navn

### 4. Lagt til Valideringsregler
- Messages-tabellen: Krav om at minst ett av `text` eller `file` er definert
- Konvertert `deliveryStatus?: string` til enum: `"sending" | "sent" | "delivered" | "failed"`
- Lagt til validering i mutation-handlere

### 5. Klargjort Timestamp-format
- Spesifisert at alle `number`-timestamps er millisekunder siden Unix epoch
- Lagt til JSDoc-kommentarer for bedre dokumentasjon
- Introdusert `timestampValidator` for konsistent validering
